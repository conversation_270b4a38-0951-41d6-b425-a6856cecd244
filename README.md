# NeXus Documentation Site

[![Built with Starlight](https://astro.badg.es/v2/built-with-starlight/tiny.svg)](https://starlight.astro.build)

A comprehensive documentation site for NeXus, built with Astro Starlight and deployed on Cloudflare Workers with secure access control.

## 🚀 Project Structure

```
.
├── public/
├── src/
│   ├── assets/           # Images and static assets
│   ├── components/       # Empty - uses Starlight components
│   ├── content/
│   │   └── docs/         # All documentation content
│   │       ├── business/     # Business strategy & models
│   │       ├── development/  # Dev practices, tools, frameworks  
│   │       ├── ops/         # DevOps, infrastructure
│   │       ├── stack/       # Technology recommendations
│   │       └── market/      # Marketing & SEO
│   ├── middleware.ts     # Authentication middleware
│   └── content.config.ts
├── astro.config.mjs      # Astro + Starlight configuration
├── wrangler.jsonc        # Cloudflare Workers deployment config
├── package.json
└── tsconfig.json
```

Starlight looks for `.md` or `.mdx` files in the `src/content/docs/` directory. Each file is exposed as a route based on its file name.

Images can be added to `src/assets/` and embedded in Markdown with a relative link.

Static assets, like favicons, can be placed in the `public/` directory.

## 🔐 Authentication & Security

This site includes built-in JWT-based authentication that can be toggled on/off via environment variables.

### Quick Setup

1. **Generate JWT Secret**:
   ```bash
   # Using Node.js
   node -e "console.log('JWT_SECRET=' + require('crypto').randomBytes(32).toString('hex'))"
   
   # Using OpenSSL  
   openssl rand -hex 32
   ```

2. **Configure Environment Variables**:
   ```bash
   # .env or .dev.vars for local development
   JWT_SECRET=your-generated-secret-here
   VALID_USERS={"admin":"password123","team":"secret456"}
   ENABLE_AUTH=true
   SITE_URL=https://docs.nexus.com
   ```

3. **For Cloudflare Workers** (more secure):
   ```bash
   pnpm wrangler secret put JWT_SECRET
   pnpm wrangler secret put VALID_USERS
   ```

### Authentication Control

- **`ENABLE_AUTH=true`**: Enables built-in JWT authentication with login page
- **`ENABLE_AUTH=false`**: Completely bypasses authentication (useful for external auth integration)

### How Authentication Works

1. **Login Flow**: Users authenticate with username/password defined in `VALID_USERS`
2. **JWT Tokens**: Secure tokens generated with `JWT_SECRET`, stored in HTTP-only cookies
3. **Session Management**: 24-hour token expiration with automatic renewal
4. **Security Features**:
   - HTTP-only cookies prevent XSS attacks
   - Secure cookies for HTTPS-only transmission
   - HMAC-SHA256 signature prevents token tampering
   - Static asset bypass for performance

### SITE_URL Configuration

The `SITE_URL` environment variable serves multiple purposes:

1. **Astro Configuration**: Used in `astro.config.mjs` as the `site` property
2. **SEO & Social Sharing**: Enables proper canonical URLs and Open Graph tags
3. **Cookie Security**: Ensures cookies are set for the correct domain
4. **Environment Management**: Different URLs for staging vs production

**Environment Examples**:
```jsonc
// Production (wrangler.jsonc)
"vars": {
  "SITE_URL": "https://docs.nexus.com"
}

// Staging  
"env": {
  "staging": {
    "vars": {
      "SITE_URL": "https://staging-docs.nexus.com"
    }
  }
}
```

## 🧞 Commands

All commands are run from the root of the project, from a terminal:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `pnpm install`            | Installs dependencies                            |
| `pnpm dev`                | Starts local dev server at `localhost:4321`     |
| `pnpm build`              | Build your production site to `./dist/`         |
| `pnpm preview`            | Preview your build locally, before deploying    |
| `pnpm astro ...`          | Run CLI commands like `astro add`, `astro check`|
| `pnpm astro -- --help`    | Get help using the Astro CLI                    |

### 🐳 Docker Development

For containerized development with live reloading:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `docker compose up -d`    | Start development server in background          |
| `docker compose logs -f`  | View live logs                                   |
| `docker compose down`     | Stop and remove containers                       |
| `docker compose up -d --build` | Rebuild and restart containers            |

The Docker setup runs the site at `http://localhost:4321` with automatic content reloading.

> **Note**: Uses `node:lts` base image instead of `node:lts-alpine` to avoid ARM64 compatibility issues with Cloudflare's workerd binary.

### Cloudflare Workers Deployment

| Command                      | Action                                        |
| :--------------------------- | :-------------------------------------------- |
| `pnpm wrangler login`        | Authenticate with Cloudflare                  |
| `pnpm wrangler deploy`       | Deploy to production                          |
| `pnpm wrangler deploy --env staging` | Deploy to staging environment        |
| `pnpm wrangler dev`          | Start local Workers development server        |
| `pnpm wrangler secret put JWT_SECRET` | Set JWT secret for production        |
| `pnpm wrangler secret put VALID_USERS` | Set user credentials for production |

## 🚀 Deployment

1. **Setup Environment**:
   ```bash
   # Generate JWT secret
   node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
   
   # Login to Cloudflare
   pnpm wrangler login
   ```

2. **Configure Secrets**:
   ```bash
   # Set production secrets
   pnpm wrangler secret put JWT_SECRET
   pnpm wrangler secret put VALID_USERS
   ```

3. **Deploy**:
   ```bash
   # Production
   pnpm run deploy
   
   # Staging (auth disabled by default)
   pnpm wrangler deploy --env staging
   ```

## 👀 Want to learn more?

Check out [Starlight’s docs](https://starlight.astro.build/), read [the Astro documentation](https://docs.astro.build), or jump into the [Astro Discord server](https://astro.build/chat).
