{
  // Cloudflare Workers configuration for NeXus Documentation Site
  "name": "nexus-docs",
  "compatibility_date": "2025-07-15",
  "compatibility_flags": ["nodejs_compat"],
  
  // Main configuration
  "main": "./dist/_worker.js",
  "assets": {
    "directory": "./dist",
    "binding": "ASSETS"
  },
  
  // Environment variables and secrets
  "vars": {
    "ENVIRONMENT": "production",
    "SITE_URL": "https://nexusdoc.lvtu.in",
    "ENABLE_AUTH": "false",
    "JWT_SECRET": "nexus-docs-jwt-secret-2025-production-key",
    "VALID_USERS": "{\"admin\":\"nexus2025!\",\"docs\":\"secure123\"}"
  },
  
  "observability": {
    "logs": {
      "enabled": false
    }
  },

  // KV namespace for session management (optional)
  "kv_namespaces": [
    {
      "binding": "SESSION",
      "id": "560b49cf25fe47449c61adae474970f8",
      "preview_id": "7081fb8b762d4a06ae246faaade43937"
    }
  ],
  
  // Custom domains (configure these after setting up Cloudflare DNS)
  "routes": [
    {
      "pattern": "nexusdoc.lvtu.in",
      "custom_domain": true
    }
  ],
  
  // Worker limits and performance
  "limits": {
    "cpu_ms": 30000
  },
  
  // Build configuration
  "build": {
    "command": "pnpm build"
  },
  
  // Development environment
  "dev": {
    "port": 8787,
    "local_protocol": "https"
  },
  
  // Environment-specific configurations
  "env": {
    "staging": {
      "name": "nexus-docs-staging",
      "vars": {
        "ENVIRONMENT": "staging",
        "SITE_URL": "https://staging-nexusdoc.lvtu.in",
        "ENABLE_AUTH": "false",
        "JWT_SECRET": "nexus-docs-jwt-secret-2025-staging-key",
        "VALID_USERS": "{\"admin\":\"staging123\",\"test\":\"test123\"}"
      },
      "routes": [
        {
          "pattern": "staging-nexusdoc.lvtu.in",
          "custom_domain": true
        }
      ]
    }
  }
}