{"name": "nexusdoc-site", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build && echo '_worker.js' > dist/.assetsignore", "preview": "astro preview", "astro": "astro", "deploy": "pnpm build && pnpm wrangler deploy", "deploy:staging": "pnpm build && pnpm wrangler deploy --env staging", "wrangler": "wrangler", "cf:dev": "pnpm build && pnpm wrangler dev", "cf:tail": "pnpm wrangler tail"}, "dependencies": {"@astrojs/cloudflare": "^12.6.1", "@astrojs/starlight": "^0.35.2", "astro": "^5.6.1", "mermaid": "^11.9.0", "sharp": "^0.34.2"}, "devDependencies": {"wrangler": "^4.27.0"}}