# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a documentation site for NeXus, built with Astro and Starlight. It serves as a comprehensive knowledge base covering business solutions, development practices, operations, and technology stack for IT solutions in the AI era.

## Development Commands

All commands use `pnpm` as the package manager:

- `pnpm install` - Install dependencies
- `pnpm dev` - Start development server at `localhost:4321`
- `pnpm build` - Build production site to `./dist/`
- `pnpm preview` - Preview production build locally
- `pnpm astro ...` - Run Astro CLI commands

## Architecture

### Tech Stack
- **Framework**: Astro 5.6+ with Starlight documentation theme
- **Deployment**: Cloudflare Pages (SSR mode with `@astrojs/cloudflare` adapter)
- **Content**: Markdown/MDX files in `src/content/docs/`
- **Styling**: Built-in Starlight theming with custom Mermaid diagram support
- **Node Version**: LTS (managed via `mise.toml`)

#### Cloudflare
- Wrangler: version over 4.27.0

### Project Structure
```
src/
├── assets/          # Images and static assets
├── components/      # Empty - uses Starlight components
├── content/
│   └── docs/        # All documentation content
│       ├── business/     # Business strategy & models
│       ├── development/  # Dev practices, tools, frameworks
│       │   ├── aidev/    # AI development tools
│       │   ├── frontend/ # Frontend technologies
│       │   ├── llm-ai/   # LLM/AI technologies
│       │   └── ...
│       ├── ops/         # DevOps, infrastructure
│       ├── stack/       # Technology recommendations
│       └── market/      # Marketing & SEO
└── content.config.ts    # Content collection config
```

### Content Organization
- Documentation is structured hierarchically with autogenerated sidebars
- Each major section (business, development, ops, etc.) has its own directory
- Uses Starlight's file-based routing (filename → URL slug)
- Supports both `.md` and `.mdx` files

### Special Features
- **Mermaid Diagrams**: Custom client-side rendering with theme support
- **Multi-language Code Highlighting**: Configured for 15+ languages
- **Cloudflare Integration**: SSR deployment ready
- **Edit Links**: Configured to point to git.lvtu.in repository

## Content Guidelines

When adding/editing documentation:
- Follow the existing directory structure under `src/content/docs/`
- Use descriptive frontmatter with proper titles
- Leverage Starlight components like `Card` and `CardGrid` for rich layouts
- Mermaid diagrams are supported with ```mermaid code blocks
- Edit links automatically point to the git repository

## Deployment

The site is configured for Cloudflare Pages deployment with:
- SSR mode enabled (`output: 'server'`)
- Site URL: `https://docs.nexus.com`
- Cloudflare adapter for edge runtime compatibility