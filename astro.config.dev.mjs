// @ts-check
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

// Development config without Cloudflare adapter
export default defineConfig({
	site: 'https://nexusdoc.lvtu.in',
	output: 'static', // Use static output for development
	integrations: [
		starlight({
			title: 'NeXus Docs',
			description: 'Pioneering IT solutions in the AI era',
			logo: {
				src: './src/assets/nexus-logo.svg',
			},
			favicon: '/favicon.ico',
			social: [
				{ icon: 'github', label: 'codebase', href: 'https://git.lvtu.in' },
			],
			editLink: {
				baseUrl: 'https://git.lvtu.in/edit/main/',
			},
			expressiveCode: {
				themes: ['github-dark', 'github-light'],
				shiki: {
					langs: [
						'javascript',
						'typescript',
						'bash',
						'shell',
						'json',
						'yaml',
						'markdown',
						'python',
						'go',
						'rust',
						'dockerfile',
						'sql',
						'css',
						'html',
						'xml',
						'toml',
					],
				},
			},
			head: [
				{
					tag: 'script',
					attrs: {
						type: 'module',
					},
					content: `
						import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@11/dist/mermaid.esm.min.mjs';
						
						mermaid.initialize({
							startOnLoad: false,
							theme: 'default',
							securityLevel: 'loose',
						});
						
						async function renderMermaidDiagrams() {
							const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid');
							
							for (let i = 0; i < mermaidBlocks.length; i++) {
								const block = mermaidBlocks[i];
								const pre = block.parentElement;
								
								if (block.dataset.mermaidRendered) continue;
								
								try {
									const code = block.textContent;
									const { svg } = await mermaid.render(\`mermaid-\${i}\`, code);
									
									const wrapper = document.createElement('div');
									wrapper.className = 'mermaid-diagram';
									wrapper.style.textAlign = 'center';
									wrapper.style.margin = '1.5rem 0';
									wrapper.innerHTML = svg;
									
									pre.parentElement.replaceChild(wrapper, pre);
									block.dataset.mermaidRendered = 'true';
								} catch (error) {
									console.error('Error rendering Mermaid diagram:', error);
								}
							}
						}
						
						document.addEventListener('DOMContentLoaded', renderMermaidDiagrams);
						document.addEventListener('astro:page-load', renderMermaidDiagrams);
					`,
				},
			],
			sidebar: [
				{ label: 'Introduction', slug: 'intro' },
				{
					label: 'Business',
					autogenerate: { directory: 'business' },
				},
				{
					label: 'Development',
					items: [
						{ label: 'Best Practices', slug: 'development/best-practices' },
						{
							label: 'API Gateway',
							autogenerate: { directory: 'development/api-gateway' },
						},
						{
							label: 'IDE',
							autogenerate: { directory: 'development/IDE' },
						},
						{
							label: 'LLM AI',
							autogenerate: { directory: 'development/llm-ai' },
						},
						{
							label: 'AI Development',
							autogenerate: { directory: 'development/aidev' },
						},
						{
							label: 'Frontend',
							autogenerate: { directory: 'development/frontend' },
						},
						{
							label: 'Node.js',
							autogenerate: { directory: 'development/node' },
						},
						{
							label: 'Python',
							autogenerate: { directory: 'development/python' },
						},
						{
							label: 'Project Management',
							autogenerate: { directory: 'development/project-management' },
						},
						{
							label: 'Versioning',
							autogenerate: { directory: 'development/versioning' },
						},
					],
				},
				{
					label: 'Operations',
					autogenerate: { directory: 'ops' },
				},
				{
					label: 'Tech Stack',
					autogenerate: { directory: 'stack' },
				},
				{
					label: 'Market',
					autogenerate: { directory: 'market' },
				},
			],
		}),
	],
});