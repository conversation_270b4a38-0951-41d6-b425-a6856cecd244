import type { MiddlewareHandler } from 'astro';
import { defineMiddleware } from 'astro:middleware';

// JWT verification utility
async function verifyJWT(token: string, secret: string): Promise<any> {
  try {
    const encoder = new TextEncoder();
    const keyData = encoder.encode(secret);
    const key = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['verify']
    );

    const [headerB64, payloadB64, signatureB64] = token.split('.');
    const signature = Uint8Array.from(atob(signatureB64), c => c.charCodeAt(0));
    const data = encoder.encode(`${headerB64}.${payloadB64}`);

    const isValid = await crypto.subtle.verify('HMAC', key, signature, data);
    
    if (!isValid) {
      throw new Error('Invalid signature');
    }

    const payload = JSON.parse(atob(payloadB64));
    
    // Check expiration
    if (payload.exp && Date.now() >= payload.exp * 1000) {
      throw new Error('Token expired');
    }

    return payload;
  } catch (error) {
    throw new Error('Invalid token');
  }
}

// Generate login page HTML
function generateLoginPage(error?: string): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeXus Docs - Access Required</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .logo p {
            color: #666;
            font-size: 0.9rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        button {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-1px);
        }
        .error {
            background: #fee;
            color: #c53030;
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        .info {
            background: #f0f9ff;
            color: #0369a1;
            padding: 0.75rem;
            border-radius: 6px;
            margin-top: 1rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>NeXus Docs</h1>
            <p>Secure Access Required</p>
        </div>
        
        ${error ? `<div class="error">${error}</div>` : ''}
        
        <form method="POST" action="/auth/login">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit">Access Documentation</button>
        </form>
        
        <div class="info">
            This documentation contains sensitive information. Please contact your administrator if you need access.
        </div>
    </div>
</body>
</html>`;
}

// Access control middleware - RUNTIME ONLY
export const onRequest: MiddlewareHandler = defineMiddleware(async (context, next) => {
  const { request, locals, cookies } = context;
  const url = new URL(request.url);
  
  // Get environment variables from Cloudflare Workers runtime
  const env = context.locals.runtime?.env || {};
  
  // Only run authentication logic at runtime when we have access to the environment
  if (!env || Object.keys(env).length === 0) {
    return next();
  }
  
  // Default to authentication enabled if not explicitly disabled
  const authEnabled = env.ENABLE_AUTH !== 'false';
  
  // Skip auth entirely if disabled
  if (!authEnabled) {
    return next();
  }
  
  // Skip auth for static assets
  if (
    url.pathname.startsWith('/_astro/') ||
    url.pathname.startsWith('/favicon') ||
    url.pathname.endsWith('.css') ||
    url.pathname.endsWith('.js') ||
    url.pathname.endsWith('.png') ||
    url.pathname.endsWith('.svg') ||
    url.pathname.endsWith('.ico')
  ) {
    return next();
  }

  // Skip authentication logic for auth endpoints (handled by API routes)
  if (url.pathname.startsWith('/auth/')) {
    return next();
  }

  // Check authentication for all other requests
  const authCookie = cookies.get('nexus_auth');
  
  if (!authCookie?.value) {
    // Create absolute URL for redirect
    const requestUrl = new URL(request.url);
    const loginUrl = new URL('/auth/login', requestUrl.origin);
    return Response.redirect(loginUrl.toString(), 302);
  }

  try {
    const jwtSecret = env.JWT_SECRET || 'default-secret-change-this';
    const payload = await verifyJWT(authCookie.value, jwtSecret);
    locals.user = payload;
    return next();
  } catch (error) {
    // Clear invalid cookie and redirect to login
    const requestUrl = new URL(request.url);
    const loginUrl = new URL('/auth/login', requestUrl.origin);
    const isHttps = requestUrl.protocol === 'https:';
    const secureFlag = isHttps ? '; Secure' : '';
    
    return new Response('', {
      status: 302,
      headers: {
        'Location': loginUrl.toString(),
        'Set-Cookie': `nexus_auth=; HttpOnly${secureFlag}; SameSite=Lax; Max-Age=0; Path=/`
      }
    });
  }
});