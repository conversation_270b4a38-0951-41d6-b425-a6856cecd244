---
sidebar_position: 1
title: Business Ideas and Innovation
sidebar_label: Ideas and Innovation
---
Idea 大熔炉。
currently exploring creative use cases of AI in lifestyle(travel) and healthcare(wellness).


> [!CORE] CORE
> 核心中的核心，还是百乐行、yili.center 的定位：Elite User Community!!! 
> 一切都是围绕经营这个用户社区出发，线上到线下，区域到全球，标准化到非标准化。
## Target Groups
这个 user community 不可能从零开始，起点在哪儿？其实探索了这么久，一直在兜兜转
- Insurance 
- Brand Users. Garmin
- Loyalty Programs. 航司、酒店、商业中心。

**second thought: loyal programs 其实代表的也是 brand 啊，只是更多的是 service industy?**

这些都是具备
- 各自不同的触达通道
- 消费场景
- 强心智。比如信用卡就不太具备强心智

所以 Youtube 是内容工具，是交流媒介，与 user community 交叉的关系。


## AI Innovation 
- joshdogford

- chrome extension is still a thing! 
- SillyTavern ~~值得~~需要持续跟进，探索 NSFW 的玩法
- n8n / MCP 是探索自动化 agentic workflow 的路径，我想尝试从给家人、朋友定制 bot 开始。

## Wellness
generic 的服务只能靠割韭菜。养老、康复这些 hardcore 的话题再议，但是我认为  wearable 的用户市场是个很大的空间，有点像通过 streaming service 的 subscriber 提供服务来提供更高的 premium 来经营 user community 的思路。他们已经为一个设备付出了几百甚至几千美金，甚至 oura 这些还有每年几百或者几千的订阅服务费用，那么我只需要在此基础上提供更高的一点点价值，就可以证明自己的 premium, 而从这个 user community 经营更多的附加生活服务，无论是旅行，还是 nutrition, 则都是可行的路径。

1. 基于 dcrainer 等社区内容，提供 spec RAG，购买指南等。现在想找寻一些规格、比较还是这么难。
2. 为 garmin connect website 做 extension， 提供更加个性化的 trans-content 的 AI 分析。比如，我上周总共跑步 30km, 历史新高，同时为了跟踪我自己的疲惫程度，我也做了大量的数据记录，比如运动后不同阶段的血压、health snapshot、睡眠前后的 HRV、呼吸训练等等，这些信息我只能靠自己交叉分析，还要自己组织语言跟 AI 对话，还需要费劲讲我的背景疑惑。太难了！

## Insurance
保诚是个大金矿，一切商业价值为导向，围绕客户价值，非常 openminded，而且都相当于小企业主。

## Travel
> 回头得写到一个 advanced table 或者使用什么 properties 的 block ，现在懒得学

我现在本来就关注的各种 airlines, accormoddation, business travel 的信息，应该用一个 agent 把它编译成几个频道，想想怎么发布。website + SEO? Youtube? HK/JP/TH 等地的本地化翻译？结合 magpiepal 的垃圾小站？

不管如何，先把数据库和 agent 拿出来，一边做一边想。

### 1. luxury travel package. based HK. 
参考[ The Grand Silk Road](https://www.goldeneagleluxurytrains.com/journeys/the-grand-silk-road/)  [Luxury Escapes](https://luxuryescapes.com/us/tour/tour-178304ba-8910-47d9-a4a3-87aed150ee10/central-asia-14d-ultra-lux-golden-eagle-silk-road-rail-tour-base)  纯做高端市场，可以作为挖掘保险高净值客户的第一步，进入一个 regent 的采购名单即可。至于进入方式，可以选择用软件服务，总之路径很长就是了
所以可能从目的地起点比较合适（sidenote: 发现还是内容，astro 还真的有搞头)

### 2. themed travel 
我一直以来的重心，需要开始积累内容啊。

