---
title: headless CMS
---

- DirectUS. 目前看来最适合 selfhost 的选项。
- strapi
- sanity.io
- Keystatic. 
- TinaCMS. 
- PayloadCMS
- ApostropheCMS. 

全都是开源项目

| Solution      | HA selfhost    | Official Docker | backend                                     | comment                                                                                                                                                   | S3 Storage                                                      |
| ------------- | -------------- | --------------- | ------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------- |
| strapi        | database       | no              | pg, mysql, sqlite3                          |                                                                                                                                                           | 不知道                                                             |
| directus      | database+redis | yes             | pg, mysql                                   | 支持 extension system                                                                                                                                       | [yes](https://docs.directus.io/self-hosted/config-options.html) |
| sanity        | no             | no              | sanity.io                                   | sanity.io 实际上是 content host provider, 开源 sanity studio 提供 content management backend. 更快速起步，口碑也很好。<br>sanity studio 还是太简单了，不适合给 maple 项目用                 | 不知道                                                             |
| Keystatic     |                |                 |                                             | 如其名，适合 all static content                                                                                                                                 |                                                                 |
| TinaCMS       |                |                 |                                             | supports Git, markdown/MDX/yaml etc.                                                                                                                      |                                                                 |
| ApostropheCMS |                | no              | mongo, pg/mysql                             | [license](https://apostrophecms.com/pricing)  按照 author 的说法，它会给 opensource 足够的能力。[core concepts](https://docs.apostrophecms.org/guide/core-concepts.html) |                                                                 |
| PayloadCMS    | yes            | no              | mongodb is 1st choice; then pg/mysql/sqlite | 与 nextjs 一起食用最佳，完全拥抱 vercel. 非常完备的功能设计, auth/search 等啥都有                                                                                                  |                                                                 |
重点评估 directus, payloadCMS. 
前端都优选 astro.js, 部署到 cloudflare, storage 用 B2

目前发现一个严重的问题，directus 不能 embed youtube video. 