---
title: Stack Options
sidebar_label: "More Options in Research"
sidebar_position: 1

---



# More Options in Research for our production stack



## python

> #python

### Database

#### drizzle v. tortoise ORM

Both Drizzle ORM and Tortoise ORM are popular choices for developers seeking asynchronous ORMs for Python, but they differ in their approaches and strengths. 

Here's a breakdown of their key features and differences:

| Feature                    | Drizzle ORM                                                  | Tortoise ORM                                                 |
| -------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| **Database Support**       | PostgreSQL (exclusive)                                       | PostgreSQL, MySQL, SQLite, Oracle (experimental)             |
| **Asynchronous Nature**    | Fully asynchronous, built with SQLAlchemy core and asyncpg   | Async-first, built on top of asyncio                         |
| **Type System**            | TypeScript-first, uses Pydantic for runtime validation       | Python type hints with runtime validation using Pydantic     |
| **Schema Definition**      | Code-first: Define schema in Python, migrations generated automatically | Schema defined in Python classes, migrations managed separately |
| **Query Language**         | SQL-like builder for constructing queries                    | Familiar Django-like ORM syntax                              |
| **Performance**            | Designed for high performance, leverages asyncpg's speed     | Generally considered fast, but potential overhead from Django-like abstraction |
| **Learning Curve**         | Might require familiarity with SQLAlchemy concepts           | Relatively easy to learn, especially for those familiar with Django ORM |
| **Ecosystem and Maturity** | Newer project, rapidly growing ecosystem                     | Mature project with a larger community and more resources    |


**Drizzle ORM Advantages:**

* **Blazing Fast:** Designed from the ground up for asynchronous performance, often outperforming Tortoise.
* **TypeScript-first Development:** Integrates seamlessly with TypeScript for enhanced type safety and developer experience.
* **Automatic Migrations:** Simplifies database management with automated migration generation and application.

**Tortoise ORM Advantages:**

* **Wider Database Support:** Offers broader compatibility with popular databases, including MySQL and SQLite.
* **Familiar ORM Syntax:** Easier to adopt for developers already familiar with Django's ORM style.
* **Mature and Stable:** Well-established project with extensive documentation, a larger community, and proven stability.

**Choosing the Right ORM:**

The best choice depends on your specific needs and priorities:

* **Drizzle ORM:** Ideal for projects demanding **maximum performance**, benefiting from a **TypeScript-first workflow**, and prioritizing **seamless database migration management**.

* **Tortoise ORM:** A strong option for applications needing **compatibility with multiple database systems**, favoring a **familiar Django-like ORM experience**, and prioritizing a **mature ecosystem** with ample resources.

Ultimately, the best way to choose is to try both ORMs on a small project and see which one better suits your development style and project requirements. 