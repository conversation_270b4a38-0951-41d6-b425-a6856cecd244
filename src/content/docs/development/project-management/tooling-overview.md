---
title: tooling overview
---


## Selfhosted
这么多年过去，这个赛道还是这么拥挤。很有意思的是，很多项目打着 opensource 的旗号，加精心设计的付费墙，比如 Planka, Focal 等。

- Redmine
- Ph... 等一堆 PHP based shits
- [Vikunja](https://vikunja.io/) 新兴个人任务管理，golang。API 正在开发中 [repo](https://kolaente.dev/vikunja/vikunja)
- [FocalBoard](https://github.com/mattermost/focalboard)
- Taiga 我部署过一次，有鸡贼的订阅 paywall，不记得是啥了
- [Plane](https://plane.so/) / [source repo](https://github.com/makeplane/plane) 看起来很酷炫 modern 的项目，更新也很频繁。`track issues, run sprints cycles, and manage product roadmaps
- [NextCloud Deck](https://apps.nextcloud.com/apps/deck)
- [wekan](https://github.com/wekan/wekan)
- [Planka](https://github.com/plankanban/planka) 草草看来，以上就是各种不同风格的看板，没啥复杂的集成
- [Leantime](https://leantime.io/) 风评很好，所谓的 People First Project Management, 界面有点丑陋。 当时最吸引我的是可以追踪 user story
- JB YouTrack

结论：
- vikunja 是很诚实的
- YouTrack 很实用，无论是自建还是直接用 cloud, 都提供 10 人的永久免费。如果真要破解，也可以用始皇的方案。我的 git repo 有记录，但是操作比较繁琐，并不总是成功。不到不得已还是不要尝试。

最后结论：
Linear rules! 