---
title: design
---


## Mockup
主力工具：miro, figma
[Figma vs. Miro](https://www.pixelperfecthtml.com/figma-vs-miro-guide-to-picking-the-best-design-tool/)
都可以用 edu 邮箱白嫖，前者还需要一个身份证明审核，后者直接用 edu 邮箱(包含 edu.* 后缀)注册，填个表格，说是要审核，但实际上立刻就通过了。到期日不明。


辅助工具：Canva. 
还是太傻瓜、平面了。也提供免费的教育计划。

## AI Design
由于我不是设计师，也不是前端开发，所以暂时还不完全理解这些 AI 设计工具的细节场景的区别。

### AI powered UI Design
- [Visily](https://www.visily.ai/)  对话式 UI 设计工具，订阅模式
- UIZard. 同上

### UI Component Design
看起来就是提供一堆 tmpl, components, 兼容 tailwindcss, shadui/cn 等，即插即用。
深度功能
- online visual editor. 允许你在他们提供的工具里在线修改 tweak 
- 跟 AI Coding 软件的整合。比如通过 mcp 一键 apply? 

- [Magic UI](https://pro.magicui.design/). 
- [Shuffle](https://shuffle.dev/)  

### full design AI
暂且这么叫吧
[21st.dev](https://21st.dev/)  

一个 [MCP](https://github.com/21st-dev/magic-mcp), 一个[comonent 库](https://21st.dev/home)，一个 AI Chat 赋能的自定义 component 界面 `Magic Chat` , 一个 [vscode extension](https://github.com/21st-dev/21st-extension) 支持所有的 AI Coding Agent

甚至 even better(or worse?), component library 开发者可以使用[这个项目](https://github.com/serafimcloud/21st)开发和提交自己的设计

组成了一个完整的开发体验，非常有前途。[定价](https://21st.dev/pricing)

#### guide
- [Guide from Cline](https://cline.bot/blog/beautiful-ui-components-how-to-use-21st-devs-magic-mcp-with-cline)
- 

另外 lovable 据说比较专注 UI Design 