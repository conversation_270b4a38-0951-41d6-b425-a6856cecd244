
---
title: Platform as a Service (PaaS)
description: Comparison and evaluation of various PaaS solutions
---

## Comparisons
大多需要 2GB mem, 但是考虑到更多的使用场景是单机部署，没这个配置也没安装的必要了。

### Dokku
看起来很酷，轻量级，照着 heroku 搬的，而且还在持续更新，够完备(proxy, dns management, Dockerfile, buildpack, zero downtime deployment 可以说样样行了)  [backup & recovery](https://dokku.com/docs/advanced-usage/backup-recovery/) 比较吸引我

但。。这[一行行](https://dokku.com/docs/deployment/application-deployment/)的命令，怎么都觉得 overkill 啊，至少现在不适合我，还不如登录上去手动管理 docker compose, 或者 Ci/CD 玩儿顺溜，然后宁愿花点时间认真学会 talos

而且它只是一个后端，不提供 frontend control panel. 自己的 panel 还在开发中。
- [shokku](https://github.com/texm/shokku)  弃坑很久了

### Dokploy

> [!NOTE] Title
> 第一选择，先把扼要的配置记录放到这里

- installation 
自动安装会占用三个 port: dokploy 的管理前端；traefik 吃掉 80/443 
如果想要个性化配置，得用 shell variable + 自己的 shell script, 在 `myops` repo 里
但即便如此，还没实验怎么给 traefik 前面再挂 caddy, 而且即便挂上，之后怎么在 dokploy 里管理 domain 呢？估计就不行吧。

- 给 service 配置 domain
比如 cloudflare, 参见[文档](https://docs.dokploy.com/docs/core/domains/cloudflare)
得先 create service, 然后 deploy, 配置好 DNS record, add/edit  domain, 然后 redeploy. 等待几秒即可


### coolify
装了，界面很傻瓜化。但是最近才开始 experimental 支持 remote servers. 看起来它的开发初衷就不是生产环境，而只是给单机部署或者 homelab 的。

放弃。

### ptah.sh
[github repo](https://github.com/ptah-sh/ptah-server)
哥们看起来是个很 experienced full stack developer, 值得信赖。但是 one man dev 的风险就是说弃坑就弃坑。
他的意见是 docker swarm 很不稳定，还是得依赖 self orchestrated k8s 方案

### easypanel
有点小众，[github](https://github.com/easypanel-io) 看起来并不是完全开源吧

## Caprover
[homepage](https://caprover.com/)

