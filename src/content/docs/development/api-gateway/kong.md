---
sidebar_position: 1
title: Kong API Gateway
sidebar_label: Kong
---

# Kong Gateway



## configuration

### Layout

Kong's configuration can be managed in several ways:

- Single File: You can have one large YAML file containing all configurations.
- Multiple Files: You can split your configuration into multiple files and use a tool like `decK` to manage them.
- Database: Configurations can be stored in Kong's database and managed via the Admin API.

For modularity, you can organize your configuration like this:

```
kong-config/
├── services/
│   ├── service1.yaml
│   └── service2.yaml
├── consumers/
│   ├── consumer1.yaml
│   └── consumer2.yaml
├── plugins/
│   ├── authentication.yaml
│   └── rate-limiting.yaml
└── main.yaml

```

Then use `decK` to sync these configurations:

```
deck sync --state kong-config
```





## Develop

for orchestration, healthcheck, 可能需要自己开发 plugin 



- fallback

```lua
local BasePlugin = require "kong.plugins.base_plugin"
local FallbackHandler = BasePlugin:extend()

function FallbackHandler:new()
  FallbackHandler.super.new(self, "fallback")
end

function FallbackHandler:access(conf)
  FallbackHandler.super.access(self)
  -- Implement fallback logic here
end

return FallbackHandler

```

- healthcheck

Types of Health Checks in Kong:

1. Active Health Checks:
   - Proactively sends requests to targets to check their health.
2. Passive Health Checks (Circuit Breakers):
   - Monitors the traffic passing through Kong to infer the health of targets.

For your specific need of launching an HTTP request and checking for certain information in the result, we'll focus on active health checks.

Active Health Check Options:

1. Basic HTTP Checks:
   - Verify that the target responds with a specific status code.
2. TCP Checks:
   - Verify that a TCP connection can be established.
3. HTTPS Checks:
   - Similar to HTTP, but over a secure connection.
4. Custom HTTP Checks:
   - This is where you can implement more complex logic, like checking the response body.



```yaml
upstreams:
  - name: complex-upstream
    healthchecks:
      active:
        type: http
        http_path: /health
        healthy:
          interval: 5
          successes: 1
          http_statuses:
            - 200
          body: 
            - '"status":"healthy"'
        unhealthy:
          interval: 5
          http_failures: 2
          http_statuses:
            - 429
            - 404
            - 500
          body:
            - '"status":"unhealthy"'
    targets:
      - target: service1:8000
        weight: 100
      - target: service2:8000
        weight: 100

```

custom plugin for complex healthcheck

```lua
local cjson = require "cjson"
local http = require "resty.http"

local CustomHealthCheck = {}

CustomHealthCheck.PRIORITY = 1000
CustomHealthCheck.VERSION = "1.0.0"

function CustomHealthCheck:init_worker()
  -- Schedule the health check
  local ok, err = ngx.timer.every(5, self.check_health)
  if not ok then
    ngx.log(ngx.ERR, "Failed to create timer: ", err)
  end
end

function CustomHealthCheck.check_health()
  local httpc = http.new()
  local res, err = httpc:request_uri("http://your-service:8000/health", {
    method = "GET",
    headers = {
      ["Content-Type"] = "application/json",
    },
  })

  if not res then
    ngx.log(ngx.ERR, "Failed to request health check: ", err)
    return
  end

  local body = cjson.decode(res.body)
  
  if body.status == "healthy" and body.version == "1.2.3" then
    -- Mark as healthy
    -- You would use Kong's Admin API here to update the target's health
  else
    -- Mark as unhealthy
  end
end

return CustomHealthCheck
```

This plugin:

1. Sends a request to a health endpoint every 5 seconds.
2. Parses the JSON response.
3. Checks for specific conditions (in this case, status and version).
4. Could be extended to update the target's health status via Kong's Admin API.





## Deployment

### caddyfile(ssl termination and HA)

```
:80 {
  redir https://{host}{uri}
}

:443 {
  tls <EMAIL>
  reverse_proxy kong:8000 kong:8000 kong:8000 {
    lb_policy round_robin
    health_check /health
  }
}
```



database migrations

```
docker run --rm \
  -e "KONG_DATABASE=postgres" \
  -e "KONG_PG_HOST=your-cockroachdb-host" \
  -e "KONG_PG_PORT=26257" \
  -e "KONG_PG_USER=your-username" \
  -e "KONG_PG_PASSWORD=your-password" \
  -e "KONG_PG_SSL=on" \
  -e "KONG_PG_SSL_VERIFY=on" \
  kong:latest kong migrations bootstrap
```

