---
title: Overview
sidebar_position: 1
---

## Overview

AI 辅助开发有如下几个场景

1. 聊天。设定 agent role, 沟通产品设计、运维、市场分析等。挑战是 persona, 聊天界面支持丰富渲染，如 mermaid(高版本)、latex、math 公式等

   1. Lobechat. 最近集成了 mermaid 渲染，上周在一次社区互动里，xu 受到挑战，号称是极大增强了长对话的渲染速度和对话首字符响应速度。
   2. OpenWebUI. 快，多 LLM 对比
   3. chatgptnextweb. 要说渲染是很全面的，但界面实在不入我的审美

   话说回来，还真没有一个能让

1. copilot. 以 github copilot 为代表，FIM, chat, inline chat

   1. Continue.Dev. vscode, jb 还是得有，常规工具，非常方便
   2. Supermaven.  FIM 超级快
   3. github copilot, codex, glm 的 codegeex....etc.  已经没必要花时间了

2. Pair Programming. 通常是 Editor 中内置

   1. Aider. 发展很早，仍然不可或缺！缺省 diff mode 编辑
   2. Cursor. 缺省 global mode. 其实 Cursor 可以当做任意 versatile AI-Powered editor 用，写剧本都行。
   3. Zed. 很奇特的思路，内置了团队协作聊天、prompt library 等，也提供官方 prompt caching 的 claude-3.5-sonnet model 能力
   4. PearAI. Continue.dev 团队的开源编辑器，尚没有看到在 continue 插件基础上有什么深度集成。目前 composer 又"借用"了 aider, 还在拼凑中
   5. Codeium. 
       1. Autocomplete 和 FIM 的口碑很好
       2. 支持几十种编辑器的 extension. 包括 vim/nvim 
       3. 支持 sonnet/o1 
       4. 支持 index remote repo(github,gitlab,bitbucket) 但我没找到配置的地方
   6. Void Editor. YC backed, 尚在 waitlist

3. project management. 我理解实际上就是 multi-agent programming. 

   1. [Google Project Oscar](https://go.googlesource.com/oscar/+/refs/heads/master/README.md)
   2. TabbyML. chat/dev/git repo integration... 本地模型友好，我还没用上
   3. Cline. 据说非常好
   4. ~~OpenDevin~~ 我其实从来没用过，但从最早的 hype 到现在仍然有用户，可能有两下子吧。

简言之，必备 kits:

- owu. 
- supermaven
- aider
- cursor

另外这两周里，git 技能明显极大挑战了我的效率，得恶补下，并且尝试用 lazygit 

## Pair Programming

- Cursor
- Trae
- Cline
- Aider
- Codeium/Windsurf

### Trae
`revert to the state before this round`  这个功能有用，可惜自动修改比起 cursor 还是拉跨。
以观后效吧。
