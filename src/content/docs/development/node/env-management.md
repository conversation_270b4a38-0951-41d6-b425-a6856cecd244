---
title: env management
---

> 目前在飞书知识库，飞书流氓不提供快速的复制 md, 将来整体解决了搬运问题再挪过来。

总之，用 `pnpm` 无论是本地开发还是 CI/CD 都是最佳选择。
[强力 promote 的文章](https://dev.to/ratul0/moving-to-pnpm-and-volta-enhancing-your-frontend-development-workflow-ogn)


```bash
# 务必用 standalone installation script, 不要用 homebrew
# 在普通权限用户使用，会安装至当前用户，不 interfere with system
curl -fsSL https://get.pnpm.io/install.sh | sh -

# 以上应该会自动添加这部分到你的 shellrc, 如果没有
export PNPM_HOME="$HOME/.local/share/pnpm"
case ":$PATH:" in
  *":$PNPM_HOME:"*) ;;
  *) export PATH="$PNPM_HOME:$PATH" ;;
esac

alias pn='pnpm'
alias pni='pnpm install'

pnpm env use --global lts
node --version
```

用 `bun` `yarn` 也不错，不过一个够了，而且 bun 的 runtime 还不够稳定，除非遇到一个项目原生支持 `bun` 的。

如果只是需要一个 nodejs runtime, 就用 [volta](https://volta.sh/) 吧。 `fnm` 也可以，同样的来自 Rust, 都非常快。
volta 的逻辑很简单，[管理环境](https://docs.volta.sh/guide/understanding#managing-your-toolchain)只需要 `install` && `uninstall` 包括 `yarn/npm` 这些 package manager. 
```
volta install node
volta install yarn@xxxxx
volta pin yarn@
```

- 关于 pnpm in volta 
[Support for `pnpm` is currently experimental](https://docs.volta.sh/advanced/pnpm)
简单说，用 volta 管理 pnpm 的安装，不要用 pnpm -g 

刚在 reddit 看到有人用 mise 作管理所有 env 的工具，[官方说明](https://mise.jdx.dev/mise-cookbook/nodejs.html)  值得一试。