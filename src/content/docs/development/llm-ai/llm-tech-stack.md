---
title: LLM Tech Stack
sidebar_position: 1
label: LLM Frameworks
---

还要参考 [[llmops]]

# Frameworks for Development

## List of Frameworks
- [HuggingFace Spaces](https://huggingface.co/spaces)
- [vLLM](https://vllm.readthedocs.io/en/latest/index.html)
- [<PERSON><PERSON><PERSON><PERSON>](https://python.langchain.com/docs/index.html)
- [LlamaIndex](https://gpt-index.readthedocs.io/en/latest/index.html)
- [LlamaHub](https://llamahub.ai/)
- [xinference](https://xinference.readthedocs.io/en/latest/index.html)

- [LitelLM](https://github.com/BerriAI/litellm) - [Docs](https://docs.litellm.ai/docs/intro)

## Data
- DataBricks
- snowflake

## Workflow Orchestration
- Dify
- Flowise.ai

## agent framework
- AutoGen
- [<PERSON>roid](https://github.com/langroid/langroid). reddit 上反响不错，发展挺久了，至今(2025 年初)还在频繁更新

### RAG
- [colpali](https://github.com/illuin-tech/colpali)  Efficient Document Retrieval with Vision Language Models
- [dsRAG](https://github.com/D-Star-AI/dsRAG)  retrieval engine for unstructured data

### chunking
- chonkie. sementic chunking, fast, high quality 



## Prompt Engineering
- [PromptWizard](https://github.com/microsoft/PromptWizard)
- [EvoPrompt](https://github.com/beeevita/EvoPrompt)
- [DSPy](https://github.com/stanfordnlp/dspy) [Page](https://dspy.ai/). Programming - not just prompting - LMs

## MCP
- [python-sdk](https://github.com/modelcontextprotocol/python-sdk)
- [browser-use](https://github.com/browser-use/web-ui) 
- [MCP-Bridge](https://github.com/SecretiveShell/MCP-Bridge)  a bridge between the OpenAI API and MCP (MCP) tools, allowing developers to leverage MCP tools through the OpenAI API interface.

## Memory
- mem0
- [memgraph](https://memgraph.com/)

## Model Development
- HF
- AutoDL
- BentoML
- Skypilot

### Post Training
- [Kiln](https://docs.getkiln.ai/). 免费工具，开源 library. 
	Kiln AI is the easiest tool for:

	- 🎛️ **Fine Tuning**: Zero-code fine-tuning for Llama, GPT4o, and Mixtral. Automatic serverless deployment of models.
	- 🤖 **Synthetic Data Generation**: Generate training data with our interactive visual tooling. 
	- 🤝 **Team Collaboration**: Git-based version control for your AI datasets


### vLLM
vLLM is a python library for running LLMs on CPU, GPU, and TPU. It provides a simple and efficient interface for LLM inference, supports both single-GPU and multi-GPU inference, and supports various LLM models, including LLaMA, GPT-J, OPT, and more.

It's capable of:

- run offline batched inference on a dataset;
- build an API server for a large language model;
- start an OpenAI-compatible API server.

[Supported LLMs](https://docs.vllm.ai/en/stable/models/supported_models.html)
