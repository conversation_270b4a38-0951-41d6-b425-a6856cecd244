---
sidebar_position: 2
title: Models
sidebar_label: Models
---
## Model Lookup
- [LLM Xplorer](https://llm.extractum.io/)
- [Model Directory by PromptHub](https://www.prompthub.us/resources/llm-model-card-directory)
- [Model Directory by LiteLLM](https://models.litellm.ai/ )  但我觉得不太准确，没有商业维护


## 关于 Provider-Model Mapping

首先必须清楚，各个 provider 上同样 model 的能力可能不同


## MaaS
- HF
- modelscope

### Post Train
- [Unsloth](https://github.com/unslothai/unsloth)  [Docs](https://docs.unsloth.ai/)  makes finetuning large language models like Llama-3, Mistral, Phi-4 and Gemma 2x faster, use 70% less memory, and with no degradation in accuracy

## Domestic

第一梯队

- Deepseek。口碑在国际上一直很好，特别是 coding 领域，在全球有至少 3 个接入点，cloudflare 友好
- qwen. 国内综合最强的开源模型，从 coder 到多模态，tabbyML 的官方文档 chat 推荐模型
- Moonshot。一直走上下文路径，但是 24 年疲软，RAG 也没搞出来什么模样。
- 豆包。语音模型，应用生态

第二梯队

- step. 最早引入超长上下文的之一。支持联网搜索、支持长文档解读、支持图像解析
- hailuo. 多模态似乎有比较多的探索。RedTeam 的逆向项目支持多轮对话、支持语音合成、语音识别、支持联网搜索、支持长文档解读、支持图像解析，零配置部署，多路token支持，自动清理会话痕迹。
- baichuan4
- ChatGLM

### qwen

- [Qwen 模型广场](https://qwenlm.github.io/blog/qwen2.5/)  应该是 qwen 模型系列 international 官方首页，有简明扼要的使用开发指南。

### deepseek
用海外 IP 全局访问 platform, 注册 API，同样可以用人民币充值。内容审查宽容很多。

### Doubao

豆包是 to C 产品，同时在火山引擎-方舟大模型服务平台提供 API 

[模型首页](https://www.volcengine.com/product/doubao)

[API 开发文档](https://www.volcengine.com/docs/82379/1263512)

[模型价格](https://www.volcengine.com/docs/82379/1099320)  直接比较了国内各大模型



## Overseas

###  Groq

提供很多免费模型，应该是自己本地部署的。提供相当稳定的 SLA， 以及相当 generous 的 rate limit. 在海外开发者社区口碑良好，广泛的被用作开发评估。

[OpenAI Compatibility](https://console.groq.com/docs/openai)


#### 免费账户的限速

- Chat Completion

| ID                                    | Requests per Minute | Requests per Day | Tokens per Minute | Tokens per Day |
| :------------------------------------ | ------------------: | ---------------: | ----------------: | -------------: |
| gemma-7b-it                           |                  30 |           14,400 |            15,000 |        500,000 |
| gemma2-9b-it                          |                  30 |           14,400 |            15,000 |        500,000 |
| llama-3.1-70b-versatile               |                  30 |           14,400 |            20,000 |        500,000 |
| llama-3.1-8b-instant                  |                  30 |           14,400 |            20,000 |        500,000 |
| llama-3.2-11b-text-preview            |                  30 |            7,000 |             7,000 |        500,000 |
| llama-3.2-11b-vision-preview          |                  30 |            7,000 |             7,000 |        500,000 |
| llama-3.2-1b-preview                  |                  30 |            7,000 |             7,000 |        500,000 |
| llama-3.2-3b-preview                  |                  30 |            7,000 |             7,000 |        500,000 |
| llama-3.2-90b-text-preview            |                  30 |            7,000 |             7,000 |        500,000 |
| llama-guard-3-8b                      |                  30 |           14,400 |            15,000 |        500,000 |
| llama3-70b-8192                       |                  30 |           14,400 |             6,000 |        500,000 |
| llama3-8b-8192                        |                  30 |           14,400 |            30,000 |        500,000 |
| llama3-groq-70b-8192-tool-use-preview |                  30 |           14,400 |            15,000 |        500,000 |
| llama3-groq-8b-8192-tool-use-preview  |                  30 |           14,400 |            15,000 |        500,000 |
| llava-v1.5-7b-4096-preview            |                  30 |           14,400 |            30,000 |     (No limit) |
| mixtral-8x7b-32768                    |                  30 |           14,400 |             5,000 |        500,000 |

- Speech To Text

| ID                         | Requests per Minute | Requests per Day | Audio Seconds per Hour | Audio Seconds per Day |
| :------------------------- | ------------------: | ---------------: | ---------------------: | --------------------: |
| distil-whisper-large-v3-en |                  20 |            2,000 |                  7,200 |                28,800 |
| whisper-large-v3           |                  20 |            2,000 |                  7,200 |                28,800 |

### Gemini Models
合作伙伴的 foundation models 都不能用 trial credit 调用了，必须绑卡。

- [Gemini Experimental Models](https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/gemini-experimental#rest) 免费，10RPM，只在 us-central1 提供
- [Gemini API pricing](https://ai.google.dev/pricing)  只列出了非 experimental 的定价

for `gemini-2-flash-lite`, default is 4K RPM and 4M TPM

#### Develop
[SDK and client libraries](https://cloud.google.com/vertex-ai/generative-ai/docs/reference/libraries)
这个[开发文档](https://ai.google.dev/gemini-api/docs/text-generation?lang=rest)还算详细
[API 结构](https://ai.google.dev/api/generate-content#FinishReason)

Gemini Model Family 的调用分为 AI Studio(Gemini API) 和 Vertex AI 两种方式，另外还出来了一个 genai SDK 目前只支持 golang 和 python. AI Studio 不稳定

要理解所有模型的调用方式，需要从如下几个方面
- Available locations. AI Studio 和 vertex ai 分别限制不同的区域，不同模型又有不同的 available locations
- Rate Limit. 不同模型在不同的调用模式有不同的 rate limit, 通常 Google 会给一个超级 generous 以至于 unlimited 的 token/day 的限制，但实际上你根本用不到——因为定睛一看，会发现有 request/min or request/day or token/min 的限制。
- change without prior notice. Google 会他妈的随意更改限制和调用方法，甚至包括模型调用 grounded search tooluse 的方式。

#### locations
- Vertex AI
	[Generative AI on Vertex AI locations](https://cloud.google.com/vertex-ai/generative-ai/docs/learn/locations#available-regions)  
	1.5-pro available locations 最多。其它模型都已经 Obsolete 了，或者用处不大。
	gemini-exp, gemini-flash-exp 都只在 us-central1 
	learnlm 不知道，调用不成功
	gemini-exp, flash-exp, 1.5-pro 都可以调用 search, 前二者跟后者的 tool 不同(参看 uni-api)
- gemini API(AI Studio) 

#### Rate Limit
- [这里](https://cloud.google.com/vertex-ai/generative-ai/docs/quotas)装模作样解释了 rate limit 的限制原则，但是完全没有提到 experimental models 的。值得注意的是提供 1.5-pro 的区域都限制 60 RPM, 如果轮询的话、再加上叠加多几个 project, 几乎没啥限制了？
- aistudio 点选某个模型，会有悬浮窗口提示
- GCP All Quotas 搜索。注意 generative language client api 和 vertex ai 的区别。

### SiliconFlow

也都是本地部署的大模型服务商，以 chat completions 为主，但据说正在快速扩张中，会优先增加视觉模型其最常用的是 deepseek, qwen 系列模型。

支持海外手机号注册，海外"优质模型"需要国内实名认证——你没看错。

- Deepseek。硅基的没法fc，上下文只有32k，回复只有4k; 官网的能fc，上下文128k，回复8k
- FLUX



### CloudFlare Workers AI



## Model Cards

LiteLLM 发布了一个工具 https://models.litellm.ai/  可以查询它支持的主流模型的能力。
没有支持的语言

Sample: 可注意到，它的 ratelimit 不一定准确

`gemini-2.0-flash-exp`

```json
{
  "max_tokens": 8192,
  "max_input_tokens": 1048576,
  "max_output_tokens": 8192,
  "max_images_per_prompt": 3000,
  "max_videos_per_prompt": 10,
  "max_video_length": 1,
  "max_audio_length_hours": 8.4,
  "max_audio_per_prompt": 1,
  "max_pdf_size_mb": 30,
  "input_cost_per_image": 0,
  "input_cost_per_video_per_second": 0,
  "input_cost_per_audio_per_second": 0,
  "input_cost_per_token": 0,
  "input_cost_per_character": 0,
  "input_cost_per_token_above_128k_tokens": 0,
  "input_cost_per_character_above_128k_tokens": 0,
  "input_cost_per_image_above_128k_tokens": 0,
  "input_cost_per_video_per_second_above_128k_tokens": 0,
  "input_cost_per_audio_per_second_above_128k_tokens": 0,
  "output_cost_per_token": 0,
  "output_cost_per_character": 0,
  "output_cost_per_token_above_128k_tokens": 0,
  "output_cost_per_character_above_128k_tokens": 0,
  "supports_system_messages": true,
  "supports_function_calling": true,
  "supports_vision": true,
  "supports_response_schema": true,
  "supports_audio_output": true,
  "tpm": 4000000,
  "rpm": 10,
  "source": "https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models#gemini-2.0-flash"
}
```


- [LLaMa 3.1](https://github.com/meta-llama/llama-models/blob/main/models/llama3_1/MODEL_CARD.md)
- [LlaMa 3.2](https://ollama.com/library/llama3.2) 支持 8 种语言
- [Distill-large-v3](https://huggingface.co/distil-whisper/distil-large-v3)
- [Mixtral 8x7B](https://huggingface.co/mistralai/Mixtral-8x7B-Instruct-v0.1)
- [Gemini](https://ai.google.dev/gemini-api/docs/models/gemini)