---
title: Audio Processing
sidebar_position: 4
label: AV Processing Models
---

# AV Processing Models
## AV Analysis Material Library

组织测试素材库，是为了测试 AI 对素材的理解。这不仅限于 speech to text, 也就是 transcribe 的过程，理想中也包含对于 metadata, video frame analysis, social data, influence 等的分析。以期让系统能对特定素材有更丰富纬度的理解，然后建立更多的连接。

事实上，于视频素材而言，这就是传统视频平台想要 achieve 的内容，只不过他们的推荐逻辑是建立在营收的基础上——更长的停留时间，以及更高的 ARPU。

所以换一个角度想，纵然 Youtube 的 monetization 一定也是驱动推荐的根本，但由于其在全球领域长视频平台的统治地位，它推荐的内容精准度极高。无论是 Home Feed, 还是分 category 的 Feed, 还是 categorization 本身，对于我这种双语阅读者，我得到的内容在专业度、兴趣的匹配度都非常高。

> **一个很"卑鄙"的套路**：给用户自动化创建 youtube 账号，然后把收藏视频都去 youtube match 上；非视频内容，让 AI 提炼关键词，去 youtube search; 然后自动化点击出播放记录。稍等片刻，youtube 就有推荐了。youtube 有推荐了并不是直接喂给用户，而是给运营人员，处理后再给技术，优化垂域模型。

### materials structure
- **X**
- 多语言音轨。语言支持 Mandarin, English(不分区域), Japanese
  - Mandarin 要测试台湾音轨。我看的台湾 vlogger 如果不自己上传，全都没字幕的。
- 是否自带 chapters 

- **Y**
- 不同类型的音轨(以英文为例，但所有语言都适用)
  - 新闻播报。字正腔圆，比较接近官方语言。可能是官媒，也可能是比较专业的自媒体。主要要选择一些地区语言，如 en-AU, en-IN
    - NBC News, Bloomberg
    - Sky News
  - 专业演讲。特定专业领域的演讲，单一发言者。一般内容包含专业领域的术语。考虑来源：podcast, Khan, TED, 流行领域(如travel, fitness..)的 KOL
    - 可能语速很快
    - 可能是英语非母语
    - 可能夹杂多语言(多为 English+?)
  - 多人会谈。常见 seminar, 采访等。如
    -  Lex Fridman 的采访，一般是双人；
    - Conference 上的 seminar 
    - 发布会。多人演讲
    - Webinar, 多人通过网络接入，可能音质录制不佳
  - 娱乐内容
    - 影视剧 clip. 
    - ？？？
- 环境噪声的类型？

时长不是问题，那是后面技术处理的事情。

编号规则
类型、语言、时长等分配代码或者编号
暂时无法在飞书文档外展示此内容

### GCP
- Transcribe Tasks 可以直接在网页端管理任务，并控制转写参数。但没看到文档那么多的选项
- Buckets
- Doc on Speaker Diarization
- Supported languages 试了中文效果比较糟糕，对话的话，还不如 youtube autogen srt
- 如果命令行操作

```
gcloud auth login
set-project ... 

# 得先创建好 bucket

gsutil cp some.mp3 gs://

gcloud ml speech recognize-long-running gs://your-bucket-name/path/to/audio.mp3 \
  --language-code=en-US \
  --enable-speaker-diarization \
  --diarization-speaker-count=2 \
  --enable-word-time-offsets \
  --model=video \
  --async
```

关于 diarization 的几个参数说明

> there are subtle differences in how these parameters work, and there might be scenarios where setting both could be beneficial:

**`min_speaker_count`:**

* **Guidance:** It provides a **lower bound** to the speaker diarization algorithm, guiding it to identify at least that many speakers. 
* **Estimation:** If the algorithm's initial estimate is lower than the `min_speaker_count`, it will try to find additional speakers to meet the minimum.
* **Not a Strict Limit:** The algorithm might still identify more speakers than the `min_speaker_count` if it detects clear speaker changes.

**`max_speaker_count`:**

* **Constraint:** It sets a **hard limit** on the number of speakers that the algorithm can identify.
* **Grouping:** If the algorithm detects more distinct speakers than the `max_speaker_count`, it will **group** some speakers together under the same label. 

**When Setting Both Could Be Useful (Rare Cases):**

* **Uncertainty:** You might have a general idea of the number of speakers but are unsure if there might be brief interjections from additional people (e.g., background voices, short comments from someone off-screen). Setting a `min_speaker_count` ensures the main speakers are identified, while `max_speaker_count` prevents the algorithm from being overly sensitive to very short speaker segments. 

**General Recommendations:**

* **Known Number of Speakers:** If you know the **exact** number of speakers, it's usually best to set **only `max_speaker_count`** (and leave `min_speaker_count` as the default or `None`). This provides a clear constraint to the algorithm.
* **Unknown Number of Speakers:** If you don't know the number of speakers, it's generally best to leave both `min_speaker_count` and `max_speaker_count` at their default values or `None`, allowing the algorithm to estimate the number of speakers based on the audio content.

### References

- Huggingface audio 
  - 分为 5 个领域：ASR, TTS, Audio Classification, Text to Music, Audio Codec Embeddings 
  - asr-leaderboard. ASR 相关模型的排行榜。除了 nvidia cuda 的几个项目，最热门的还是 openai-whisper 以及 distill-whisper 后者我试了一下，不知道怎么用中文 
- whisper-large-v3-turbo Whisper large-v3-turbo is a finetuned version of a pruned Whisper large-v3. In other words, it's the exact same model, except that the number of decoding layers have reduced from 32 to 4. As a result, the model is way faster, at the expense of a minor quality degradation. You can find more details about it in this GitHub discussion. 
- ModelScope  国内仿 HF 的站，很多模型只是来推广的
- Discussion board on openai-whisper  openai-whisper 离线使用的优势，吸引了很多二次开发和模型优化。这里可以得到很有价值的 insights & practices. 
- FunASR
- faster-whisper  seriously ，可以跟 distill-whisper 一起看，有很多有趣的社区 spawned projects。有详细的运行效果比较，但是尚未用在自己的实测。
  - faster-whipser-server
  - https://github.com/shashikg/WhisperS2T
  - https://huggingface.co/Systran/faster-whisper-large-v3 
  - WhisperX
  - Whisper diarization
  - 以上两个非常重要。It's important to note that it's ctranslate2 and not just faster-whisper. As far as I know, whisperX and whisperS2T are the only repositories that have batch processing using ctranslate2. faster-whisper should hopefully be getting it soon 参见这个 benchmark 
- Insanely fast whisper 以上基础之上的改进，关注着似乎不多？
- Google Cloud Speech-to-Text API
- Azure OpenAI Whisper / Azure AI Speech batch transcription API 。OpenAI Whisper Model 跟 openai whisper-1 一样最大只接受 25MB 的文件输入， Azure AI Speech 也可以调用 whisper-1 对 1GB 以下的文件进行批量转写。

为了突破 whisper API 调用 25MB 的 size 限制，有人说可以用 ogg 压缩
ffmpeg -i input -vn -map_metadata -1 -ac 1 -c:a libopus -b:a 12k -application voip output.ogg
确实大大降低了文件大小，而且人耳听起来损失不显著。但是即便符合 whisper-1 25MB 的文件大小限制，如果时长太高，也会被拒绝，没有明确公告标准。
另外，也尝试过丢给离线模型，同样的，结果差别不大。
Notable tools
- vibe 使用 openai-whisper 离线 transcribe 的客户端工具，速度快，支持 diarization，更新还挺频繁，有个 discord 社区， 得好好研究代码。安装说明 
- GPT-SoVITS 国产开源软件，号称超越 openTTS, 豆包等一系列自定义语音输出 TTS，变声功能在开发中。demo 中看来，带有抑扬顿挫的多语言输出效果相当惊艳。详细文档  可用 AutoDL 等云端服务便宜(据说训练一次模型几块钱)的训练自己的模型。我还没上手实验。8 月底刚发布了 v2, 我准备等一个月再用

## ASR(STT)

### Models
- whisper 系列还是主流
- [RealtimeSTT](https://github.com/KoljaB/RealtimeSTT)

简单理解，Transcribe 要么生成文本，要么生成带时序信息的字幕文件。

Quick run

```
- 用 openai-api CLI
设置环境变量
export OPENAI_API_KEY="cant-be-empty"
export OPENAI_BASE_URL=http://localhost:8000/v1/
  -m MODEL, --model MODEL
  -f FILE, --file FILE
  --response-format RESPONSE_FORMAT
  --language LANGUAGE
  -t TEMPERATURE, --temperature TEMPERATURE
  --prompt PROMPT
  
openai api audio.transcriptions.create -m Systran/faster-distil-whisper-large-v3 -f audio.wav --response-format text

openai api audio.translations.create -m whisper-1 -f audio.wav --response-format verbose_json
```

cURL

```
# If `model` isn't specified, the default model is used
curl http://localhost:8000/v1/audio/transcriptions -F "file=@audio.wav"
curl http://localhost:8000/v1/audio/transcriptions -F "file=@audio.mp3"
curl http://localhost:8000/v1/audio/transcriptions -F "file=@audio.wav" -F "stream=true"
curl http://localhost:8000/v1/audio/transcriptions -F "file=@audio.wav" -F "model=Systran/faster-distil-whisper-large-v3"
# It's recommended that you always specify the language as that will reduce the transcription time
curl http://localhost:8000/v1/audio/transcriptions -F "file=@audio.wav" -F "language=en"

curl http://localhost:8000/v1/audio/translations -F "file=@audio.wav"
```

几轮测试下来，关于联网模型调用

- whisper-1 暂时不支持区分 speaker, 亮点是参数配置简单:  prompt, lang, temperature 就可以出一篇 article 级别的转录结果
- GCP 的参数更为复杂，只有 v1 支持 diarization, 艰苦的测试下来，即便是多语言的效果也不理想，可能还不如 Youtube 自动翻译的结果
- Groq. 非常快，效果跟 oai 的相差无几，多语言也可以用。体感：1 分钟音频(mp4, youtube format 140) = 10 秒
- AssemblyAI. 异步大文件转写，速度是 Groq 的 1/5 左右。
- Openai 的接口直接通过 HTTP Post 发送音频文件，Azure, GCP 都接受 object storage 的 uri 输入。

关于离线模型调用

- 还是基于 openai-whisper 的居多，但是模型文件优化也很多
- 效果还不错，但是多语言支持不明
- 多灵活支持输出文件的格式(srt, vtt, md, json...)
- 考虑到联网模型的网络传输时间，以及可能的需要 segmentation 的时间，离线模型也不见得很慢。但确实在语言的自然程度上，很难打得过 whisper-1  

## Tech
引用自 [cnbeining@linuxdo](https://linux.do/u/cnbeining) :
- VAD: 有效减少hallucination 例如 [Silero VAD](https://github.com/snakers4/silero-vad) also see [GitHub - bigcash/awesome-vad: A curated list of awesome voice activity detection](https://github.com/bigcash/awesome-vad)
- Text to Speech: Whisper V2 例如 [Faster-Whisper] ([GitHub - SYSTRAN/faster-whisper: Faster Whisper transcription with CTranslate2](https://github.com/SYSTRAN/faster-whisper)) / [Groq](https://console.groq.com/docs/speech-text)
    - 注意 Whisper 可以[调prompt加入语料库](https://github.com/alphacep/whisper-prompts)
- Forced Alignment 得到字级别timestamp
    - Faster-Whisper 直接[在 tokenizer 上](https://github.com/SYSTRAN/faster-whisper/blob/master/faster_whisper/tokenizer.py#L93-L106) end-to-end 实现了FA
    - 部分第三方 API 也直接提供FA 例如 [Fireworks.AI](https://docs.fireworks.ai/api-reference/audio-transcriptions#param-timestamp-granularities)
    - See [Tradition or Innovation: A Comparison of Modern ASR Methods for Forced Alignment](https://arxiv.org/html/2406.19363v1)
- 断句：可以使用比较先进的 LLM
    - 也可以使用 [ACI 字幕组的私有模型](https://huggingface.co/cnbeining/OpenHermes-2.5-Mistral-7B-Sentence-Segmentation-merged)
- 精校：需要先进 LLM
    - STT质量是否已经足够翻译？注意过度校对有可能造成大量hallucination
- 翻译：需要先进 LLM
    - 建议使用 [Agently](https://agently.cn/guides/agentic_request/guide.html#_13) 协助开发
- 字幕生成：建议从[ACI 字幕组的脚本](https://github.com/cnbeining/Whisper_Notebook/blob/master/Faster_Whisper_Public.ipynb) 直接抄

> 然后我问关于 speaker diarization

取决于音频质量和语言。

质量问题可以看输出： [GroqCloud](https://console.groq.com/docs/speech-text#using-metadata-for-debugging)

解决质量问题不容易 可以考虑 [GitHub - EtienneAb3d/WhisperHallu: Experimental code: sound file preprocessing to optimize Whisper transcriptions without hallucinated texts](https://github.com/EtienneAb3d/WhisperHallu) 的思路：

- 试试用人声分离器
- 用 VAD 切掉静音和非人声
- 试试人声压缩
- 试试给每段人声加个 marker 然后移除
- 试试给 Whisper 的 prompt 加词库

### TTS

tts-1  处理 2min 大概花了 $0.03

- ChatTTS 据说效果惊艳，但是支持英文和中文两种语言。最大的模型使用了10万小时以上的中英文数据进行训练。在HuggingFace中开源的版本为4万小时训练且未SFT的版本.

#### RealtimeTTS
[RealtimeTTS](https://github.com/KoljaB/RealtimeTTS)
- **Multiple TTS Engine Support**
    - supports OpenAI TTS, Elevenlabs, Azure Speech Services, Coqui TTS, StyleTTS2, Piper, gTTS, Edge TTS, Parler TTS, Kokoro and System TTS

#### Google
[TTS Engine Demo](https://cloud.google.com/text-to-speech?hl=en)

#### edge-tts
edge-tts 顾名思义，就是使用 Edge 浏览器内置的免费语音合成的功能，反向工程用于生成语音的 SDK
- [edge-tts](https://github.com/rany2/edge-tts/)  此类始祖项目，pypi package. 
- [node-edge-tts](https://github.com/SchneeHertz/node-edge-tts)  受以上项目启发，独立的 nodejs package
在此 SDK 基础上 ，最广泛使用的方法就是包装成 openai speech api 兼容的 API
- [openai-edge-tts](https://github.com/travisvn/openai-edge-tts)  travisvn 的版本，便于 docker 部署，使用以上 `edge-tts` python 的库。缺省输出 speed 1.2 倍速。提供一个非常好用的 [voice samples repo](https://tts.travisvn.com/) 便于选择所有的音色，但是我没找到 MultiLingual 的音色，比如 `en-US-AvaMultilingualNeural` 

#### hailuo-tts
目前看来只有中国区的[海螺 AI ](https://hailuoai.com/)支持全系列的模型服务，包括图文、音色克隆、语音合成等，必须使用手机号注册，可以用接码平台，但是 access_token 得时常刷新。
使用[ REDTEAM 逆向](https://github.com/LLM-Red-Team/minimax-free-api?tab=readme-ov-file#%E5%85%8B%E9%9A%86%E5%8F%91%E9%9F%B3%E4%BA%BA)项目可以用