---
sidebar_position: 1
title: Continuous Integration and Continuous Deployment (CI/CD)
sidebar_label: CI/CD
last_update: 2025-08-01
---

# Continuous Integration & Continuous Deployment (CI/CD)

> **Scope**
> This living document captures NeXus’s current CI/CD approach with an emphasis on **cost‑effectiveness**, **edge scalability**, and **developer simplicity**. It supersedes earlier drafts (2024‑07) and reflects our 2025 research on self‑hosted runners, k3s clusters, and global single‑node patterns.

---

## 1  Why CI/CD?

| Goal                          | Outcome                                                                         |
| ----------------------------- | ------------------------------------------------------------------------------- |
| Shorten feedback loops        | Faster detection of integration issues, test failures, and security regressions |
| Increase deployment frequency | Small, incremental releases with automated rollbacks                            |
| Lower infrastructure spend    | Scale‑to‑zero runners and pay‑per‑second cloud build minutes                    |
| Uniform developer experience  | One YAML‑based workflow definition for all projects                             |

---

## 2  High‑Level Pipeline

```mermaid
flowchart LR
  SC[(Source Code)] --> CI["CI Jobs <br/>– lint<br/>– unit test<br/>– build image"]
  CI --> CD["CD Jobs <br/>– deploy to staging <br/>– e2e tests <br/>– promote to prod"]
  CD --> Mon[Monitoring / Alerting]
```

*Each workflow is triggered by Git events or a manual dispatch and runs on the cheapest runner that satisfies the required labels (e.g. `arm64`, `gpu`, `privileged`).*

---

## 3  Platform Matrix (Free & Low‑Cost Tiers)

| Platform                   | Free Hosted Minutes<sup>†</sup> | Concurrency            | Self‑Hosted Runners       | ARM 64 Hosted                   | Notes                             |
| -------------------------- | ------------------------------- | ---------------------- | ------------------------- | ------------------------------- | --------------------------------- |
| **GitHub Actions**         | 2 000 private / ∞ public        | 20 Linux               | Unlimited, feature‑parity | GA <br/>`runs-on: ubuntu‑arm64` | Marketplace of 20 k+ actions      |
| **GitLab CI (gitlab.com)** | 400                             | Unlimited<sup>\*</sup> | **Unlimited & free**      | No (self‑host)                  | Bring‑your‑own runner = zero cost |
| **Bitbucket Pipelines**    | 50                              | 10                     | Yes                       | Beta                            | Best for Atlassian stack          |
| **CircleCI Free**          | 6 000                           | 30 Docker              | Yes                       | Yes                             | Orbs ecosystem                    |
| **Semaphore Free**         | 7 000                           | 40                     | Yes                       | Yes                             | Pay‑per‑second after quota        |
| **GCP Cloud Build**        | 2 500                           | 10                     | —                         | Multi‑arch via Buildx           | Tight GCP integration             |
| **Azure Pipelines**        | 1 800                           | 1 hosted / ∞ self      | Yes                       | Self‑host                       | Good for hybrid Windows           |
| **Vercel (Hobby)**         | ∞ builds (≤ 45 min)             | 1                      | —                         | —                               | Edge preview URLs                 |

<small><sup>†</sup> Hosted minute multipliers: Linux 1×, Windows 2×, macOS 10×. <sup>\*</sup>Bound only by runner count.</small>

---

## 4  Recommended Tooling

### 4.1  Self‑Hosted GitLab Runner (🏆 Cost Winner)

* **Why**: Unlimited minutes, ARM/x86 parity, Docker/Kubernetes executors.
* **Deploy pattern**: one GitLab Runner **Helm chart** per k3s single‑node cluster (US/EU/AP). Set **HPA/KEDA** to scale manager to 0 when idle.
* **Resource caps**:

  ```toml
  [[runners]]
    executor = "docker"
    [runners.docker]
      cpus   = "1"
      memory = "1g"
  ```
* **Cost**: each 512 MB VM ≈ US\$5/mo; scales to zero job pods when idle.

### 4.2  GitHub Actions (Public OSS)

Free unlimited minutes for public repos + huge action marketplace. Private workloads can off‑load heavy jobs to **self‑hosted ARM64** VMs tagged `self-hosted,arm64`.

### 4.3  CircleCI & Semaphore (Burst Capacity)

Use the generous 6 k–7 k minute free tiers for short‑lived spike projects. Orbs (CircleCI) / Blocks (Semaphore) offer one‑line deploys to Cloudflare, GCP, and Vercel.

---

### 4.4  Drone CI (Container‑Native, OSS)

* **Why** Fully open‑source, self‑hosted CI that executes every pipeline step in an isolated container.
* **Deploy pattern** Single Go binary + SQLite/Postgres *or* Helm chart on existing k3s nodes (≈ 200 MB RAM).
* **Cost** **\$0 licence**; pay only for the VM. Unlimited build minutes, ARM/x86 parity.
* **Highlights** DAG‑style YAML, secrets store, native Docker & Kubernetes runners, auto‑scaling agents.

### 4.5  Argo CD (GitOps‑First CD)

* **Why** Kubernetes‑native, pull‑based **delivery** controller that keeps clusters in sync with Git.
* **Deploy pattern** Helm/Kustomize install in any cluster; can manage **multi‑cluster** fleets.
* **Cost** **\$0** (Apache‑2.0). Control‑plane < 300 m CPU; idle‑scale via HPA.
* **Highlights** Declarative sync/rollback, health checks, SSO, RBAC, GUI, progressive delivery with **Argo Rollouts**.

## 5  Edge & Global Runner Topology

| Region         | Pattern         | Runner Type                    | Monthly Idle Cost | Notes                        |
| -------------- | --------------- | ------------------------------ | ----------------- | ---------------------------- |
| us‑east‑1      | k3s single‑node | Helm GitLab Runner             | \~US\$5           | 1 vCPU / 512 MB VM           |
| eu‑central‑1   | k3s single‑node | Helm GitLab Runner             | \~€5              | Same stack                   |
| ap‑southeast‑1 | Docker‑only     | gitlab/gitlab‑runner container | \~US\$3           | Cheaper but no pod isolation |

> *Clusters are independent*—a down region’s jobs are re‑queued and picked up by the next available runner with matching tags.

---

## 6  ARM & Multi‑Architecture Builds

* **Docker Buildx** in CI enables `linux/amd64` *and* `linux/arm64` images from the same workflow.
* Hosted ARM64 exists on GitHub Actions, CircleCI, Semaphore; otherwise register your OWN ARM runners (Raspberry Pi 5, AWS Graviton, etc.).

Example GitLab job:

```yaml
build:arm64:
  image: docker:24
  services:
    - docker:24-dind
  variables:
    DOCKER_BUILDKIT: 1
  script:
    - docker buildx create --use --name multi
    - docker buildx build --platform linux/amd64,linux/arm64 -t registry.example.com/app:$CI_COMMIT_SHA . --push
```

---

## 7  Cost‑Saving Playbook

1. **Prefer self‑hosted runners** for CPU‑intensive stages (build, test).
2. **Cap resources** (`cpus`, `memory`) per runner / pod.
3. **Use `needs:` & `concurrency:`** to avoid parallel waste.
4. **Cache dependencies** (npm, pip, cargo) between jobs.
5. **Scale‑to‑zero** runner managers via KEDA or HPA.
6. **Public OSS?** Offload to GitHub Actions unlimited tier.

---

## 8  Monitoring & Alerting

| Signal             | Tool                       | Threshold                          |
| ------------------ | -------------------------- | ---------------------------------- |
| CI queue length    | GitLab Prometheus exporter | > 5 pending jobs triggers scale‑up |
| Runner pod CPU     | kube‑metrics‑adapter → HPA | > 80 % for 5 m                     |
| Build failure rate | Sentry CI integration      | > 10 % failures / day              |

Alert routing via PagerDuty; dashboard in Grafana `ci-cd/main`.

---

## 9  Rollback & Deployment Strategies

| Strategy             | When to use                               | Tooling                                 |
| -------------------- | ----------------------------------------- | --------------------------------------- |
| **Blue‑Green**       | Backend services where cold starts are OK | Kubernetes `service.selector` switch    |
| **Canary**           | Gradual rollout to 10 % traffic           | Istio / Cloudflare Load‑Balancing rules |
| **Instant Rollback** | Static sites & edge functions             | Vercel `vercel rollback <id>`           |

---

## 10  Best Practices Checklist

* [ ] All repos contain a **one‑click setup** `templates/ci.yml` preset.
* [ ] Secrets are stored in **Vault** / GitHub Secrets, never plain YAML.
* [ ] CI statuses block merges unless *all* required jobs pass.
* [ ] Each pipeline finishes in **≤ 10 minutes** (use caching, parallel tests).
* [ ] SBOM published on every release (`cyclonedx‑bom` action).
* [ ] Production deployments are **tag‑triggered** (`v*.*.*`).

---

## 11  Further Reading

* [GitLab Runner Helm chart](https://docs.gitlab.com/charts/)
* [k3s Lightweight Kubernetes](https://k3s.io/)
* [KEDA: Kubernetes Event‑Driven Autoscaling](https://keda.sh/)
* [GitHub Actions Runner Resource Limits](https://docs.github.com/en/actions/hosting-your-own-runners/about-self-hosted-runners#using-labels-with-self-hosted-runners)

