---
title: Image
---


Image CDN 核心问题是在边缘侧加载图片，并根据端侧带宽和处理能力动态传输不同规格的内容。

大体这几个方案
- CloudFlare Stream. 现在 image/video/audio 整合到了[一个方案](https://dash.cloudflare.com/c8c2444324bd19865d0924d0b606fcc7/stream/plans)
- [CloudFlare Image](https://developers.cloudflare.com/images/). 提供图片 storage/transfer/optimize 的一整套方案，Polish 专门用于 optimize image transfer without sacrificing quality
- [wsrv.nl ](https://wsrv.nl/) [开源](https://github.com/weserv/images)，也提供免费的公共服务。
- Vercel 自己也有 Vercel Image
- WebP 

这两篇文章虽然都写于 2023 年，但非常深入扎实
- [自建 CloudFlare Worker 处理图片](https://chi.miantiao.me/posts/cloudflare-worker-image/)
- [2023 年图片 CDN——Cloudflare Polish, Bunny Optimizer, Vercel Image Optimization, imgproxy 和 WebP Cloud Services 对比](https://blog.webp.se/2023-cdn-compare-zh/)
- [2024 WebP Cloud 的最新对比](https://blog.webp.se/webpcloud-polish-compare-zh/)

总体上 [WebP Cloud](https://webp.se/) 是一个很有技术功底的独特产品定位，可以适度的应用实验。