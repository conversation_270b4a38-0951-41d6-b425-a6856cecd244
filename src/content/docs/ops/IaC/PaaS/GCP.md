---
title: GCP
---


---
## 目录
1. [引言](#引言)
2. [Free Trial（免费试用）](#free-trial免费试用)
3. [Always Free Tier 详细配额](#always-free-tier-详细配额)
   - Cloud Run
   - Cloud Functions
   - Compute Engine
   - Cloud KMS
4. [Always‑Free VM 实例创建清单](#always‑free-vm-实例创建清单)
5. [Usage Tips：如何防止费用超额](#usage-tips如何防止费用超额)
6. [配置双栈 IP Network Interface](#配置双栈-ip-network-interface)

---
## 引言
Google 引以为傲的基础设施能力毋庸置疑，GCP 也因此成为工程师取向浓厚的 PaaS。然而，其产品与控制台交互体验常被诟病“反人类”。Google 几乎所有云服务——包括 Maps、Vertex AI、BigQuery、Kubernetes 等——都在 GCP 门下，因此**吃透免费策略**是团队成本优化的必修课。

---
## Free Trial（免费试用）
GCP 免费试用条款涵盖 **\$300 美元赠金** 与配额限制，原文要点完整保留：

1. **\$300 免费赠金**：新用户绑定信用卡后即可获得，试用期 **90 天**，可抵扣任意 GCP 服务。 
2. **Always‑Free Tier**：Compute Engine 额外提供 1× `e2‑micro` VM + 30 GB HDD + 5 GB 快照（仅限 `us‑west1`／`us‑central1`／`us‑east1`）。
3. **Standard Tier 网络出网**：自 2023‑09 起，**200 GB/月** 标准网络层（Standard Tier）免费出网额度（Cloudflare 等 CDN 流量不计入）。
4. **试用期配额**：单项目最多 8 vCPU；无法申请 GPU / Cloud TPU；配额不足时亦无法工单升配。  
5. **服务中断风险**：赠金或时限耗尽、或违反 ToS，将导致实例被强停，无 SLA 保障。
6. **数据丢失风险（官方未证实）**：试用期满后 30 天内若未升级计费账号，项目及数据可能被清除且无法恢复。

---
## Always Free Tier 详细配额
> 下表基于 2025‑08 官方 Pricing 文档整理。所有额度**按月重置**、**可与 \$300 赠金叠加**。

| 服务 | 主要免费额度 | 备注 |
|------|-------------|------|
| **Cloud Run** | ‑ 2 M 请求/月  <br>‑ 180 k vCPU‑秒/月  <br>‑ 360 k GiB‑秒/月  <br>‑ 1 GiB 出网（Premium Tier，北美） | 请求可并发；单实例 60 min 超时；超额后按秒计费 |
| **Cloud Functions (2nd Gen)** | ‑ 2 M 调用/月  <br>‑ 400 k GB‑秒/月  <br>‑ 200 k GHz‑秒/月  <br>‑ 5 GB 出网 | 1st Gen 配额相同；单调用 9 min 超时 |
| **Compute Engine** | ‑ 1× `e2‑micro` (720 h)  <br>‑ 30 GB 标准持久盘  <br>‑ 5 GB 快照  <br>‑ 1 GiB 出网 (Premium Tier) <br>‑ 200 GB 出网 (Standard Tier) | 仅限 `us‑west1 / us‑central1 / us‑east1` 区域 |
| **Cloud KMS** | ‑ 活跃密钥版本 ≤ 100 个/月  <br>‑ 10 k API 调用/月 | 适用于对称和非对称密钥 |
| **Cloud Pub/Sub** | **10 GB** 传输量（消息发布 + 投递总计） | ⬆ 持续 | ([cloud.google.com](https://cloud.google.com/free/docs/free-cloud-features)) |
| **Google Maps Platform** | **Essentials 计划：10 K 免费调用/每 SKU**  <br>**Pro 计划：5 K**  <br>**Enterprise：1 K** | ⬆ 2025-03 起：取消 $200 月度抵扣，改为固定位调用上限 | ([mapsplatform.google.com](https://mapsplatform.google.com/pricing/))|

> **备注**：Google Maps Platform 仍需启用账单并绑定付款方式；超过免费调用将按 SKU 单价计费。建议在 *Billing → Quotas* 设置上限，防止突发费用。

---
## 参考链接

> **为什么 Cloud Run / Functions 出网只有 1–5 GiB？** 这两项服务始终使用 **Premium Tier** 网络，Compute Engine 才有 200 GB Standard Tier 额外福利。

所以 cloud run/functions 虽然是 scale up 的利器，但是出网流量受限，使用场景也受限。

---
## Always‑Free VM 实例创建清单
> 以下 checklist 原文照搬并略作排版优化，确保符合 Always‑Free 条件。

- **CPU 类型**：`e2.micro`
- **区域**：
  - `us‑west1` (Oregon)
  - `us‑central1` (Iowa)
  - `us‑east1` (South Carolina)
- **磁盘**：Standard Persistent Disk，30 GB
- **Backups**：关闭
- **Firewall**：允许 HTTP/HTTPS
- **Networking › Network Interfaces**
  - IPv4 single‑stack
  - 启用 IP Forwarding
  - 服务等级：**Standard Tier**（200 GB 免费），切记不要用默认的 Premium
- **SSH Key**：优先使用 **Project‑wide** 公钥；若无，按 `ssh‑rsa … <EMAIL>` 格式追加
- **Optional**：Deletion Protection = Enabled
- 创建后修改 Network Interface Firewall 以放通 SSH (`tcp/22`)
- 控制台估算月费 ~\$7.3（2024‑05），属正常，忽略即可

---
## Usage Tips：如何防止费用超额
1. **Billing Alert**：为每个项目设置月度限额；在 *Billing → Budgets & Alerts* 启用邮件 / SNS 通知。
2. **受限信用卡**：使用额度较低或已设消费上限的信用卡，避免异常扣费。
3. **监控账单**：定期查看 *Cost Explorer*，并启用 *Cost Anomaly Detection*。
4. **CDN 注意事项**：避免直挂 Cloudflare；若需全球加速，请使用 Cloud CDN，否则 Standard Tier 200 GB 出网无法抵扣。若必须走 CF，请结合 Tailscale 或 DNS‑based Service Discovery 降低防火墙风险。

---
## 配置双栈 IP Network Interface
> （原文保留占位，实际配置步骤请参见 GCP 官方文档：<https://cloud.google.com/vpc/docs/adding-ipv6-addresses>）

