---
title: storage
---

# Storage 

由于存储方案一旦决定，迁移成本将会很高，需要结合应用场景，综合考虑
- 稳定性
- 可迁移能力
- 价格策略。在线存储不是买硬盘，不要被存储容量误导，API 请求、读/写流量、甚至文件类型都可能构成价格策略


## Object Store
只考虑 S3 compatible 

### selfhost
只作为开发环境的使用，生产环境不考虑

- minio

### Services
基本上入围的只有 CF R2/BB B2

#### Amazon S3, GCP, Azure 等
贵，pass

#### CloudFlare R2
- [Detailed Pricing](https://developers.cloudflare.com/r2/pricing/)

tl;dr
- 存储位置：亚洲、欧洲、美洲。但是创建 bucket 的时候要指定区域
- 存储费用：1T/15 美元【不超过10GB免费】
- A类操作(上传)：每月 100W次免费【超过按4.50 美元/百万次】
- B类操作(下载)：每月 1000W次免费【3.6 美元/千万次】
- 流量：【免费】

计费标准是统一的，workers paid, cf pro 也没有更高的额度。

#### BackBlaze B2
- [Overall Pricing](https://www.backblaze.com/cloud-storage/pricing)
- [API Pricing](https://www.backblaze.com/cloud-storage/transaction-pricing)

存储位置：目前只有美国、欧洲
存储费用：1T/6 美元，前 10G 免费。现在如果要 public bucket access, 需要先验证信用卡，支付 $1
A操作（上传）：免费
B操作（下载）：每天2500次（免费）| 然后每10000次【0.004美元】
C操作：每天2500次（免费）| 然后每1000次【0.004美元】。包含：创建API、复制、分块上传、创建桶、获取存储桶的信息、获取存储桶内文件列表

如果测试直连桶的速度，CF 的速度更优。但是很显然为了规避不可控的流量费用，一定是使用 Fastly/CF Workers 来访问 B2 桶，那么流量费用和 edge network transfer 也跟 CF 一样了。

所以用 CF 快速开发的时候，就还是用 R2, 实际生产环境还是用 B2.

## Distributed Storage
- zfs
- ipfs
- cypher 等 k8s native 的 storage plan

