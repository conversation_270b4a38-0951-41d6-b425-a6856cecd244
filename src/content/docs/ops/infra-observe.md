---
title: Infrastructure Observability
description: Monitoring and observability solutions for infrastructure
---

几个监测级别
- server connection. 基本的 OS up/down 
- service uptime. 针对 service healthy 
- service quality.  API response time, API 是否 work(LLM 返回正确答案)
- load test. 

目前常用的方案有

## Hosted
### [uptimerobot](https://dashboard.uptimerobot.com)
老牌，极简，VPS 圈里很流行。
free tier 不支持 custom domain, monitor 只有 5 个？incident history 很短；check freq 5min; 不支持 status page

### [newrelic](https://newrelic.com/)
更专业的，更开发向的
还没试用过

### [betterstack](https://betterstack.com/)
很好用，挺专业。maplenova 的所有项目就用这个吧。
free tier 支持 custom domain, 支持 status page(1), incident history 2months, check freq 3min, 10 monitors

可以用于配置 LLM API 监控

### datadog
当然原则上 datadog 也可以

## Selfhosted
### uptime flare
很流行，无论国内还是国外。

### upptime 
[docs](https://upptime.js.org/docs/)
host 在 github 上，purely serverless. 但是也有限制

- Using GitHub Actions, users can [schedule](https://docs.github.com/en/free-pro-team@latest/actions/reference/events-that-trigger-workflows#schedule) workflows to automatically run every x-minutes. The shortest interval is 5 minutes.

### uptimeflare
使用 cloudflare worker 的方案，purely serverless. 

最重要的限制是 uptime history max 90 天。[github discussion](https://github.com/lyc8503/UptimeFlare/issues/92#issuecomment-2731990676)

目前 90 天的限制主要来自于两个限制：

1. Cloudflare Workers 对每次 CRON 执行消耗的 CPU 时间有限制，免费账号为 10ms，付费账号为 15min，parse 一个过大的 JSON 会直接超时
2. Cloudflare Workers 对 D1 或者 KV 存储的单个值有大小限制，存储过多的历史可能超出该限制，这个限制付费也无法提升

	以 D1 为例，其限制单个 BLOB 不能超过 2MB，目前我的主页有七个监控，90 天的历史记录已经消耗了近 200 KB，如果有更多监控那消耗的还会继续线性增长。根据我的测试，免费账号的 JSON 大小达到 1MB 时，就有可能导致免费账户运行超时了。即使是付费账户，这个 JSON 也不能超过 2MB。

所以没办法。

当然，我也可以自己开发，使用 supabase 这些外置数据库。但完全没必要了

### gatus
[demo](https://gatus.io/demo)
看起来也很好，用 golang 的

### checkmate
似乎是更加全面的设计，接近我的需求




