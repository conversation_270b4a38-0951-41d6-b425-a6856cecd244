import type { APIRoute } from 'astro';

// Generate login page HTML
function generateLoginPage(error?: string): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeXus Docs - Access Required</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .logo p {
            color: #666;
            font-size: 0.9rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        button {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-1px);
        }
        .error {
            background: #fee;
            color: #c53030;
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        .info {
            background: #f0f9ff;
            color: #0369a1;
            padding: 0.75rem;
            border-radius: 6px;
            margin-top: 1rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>NeXus Docs</h1>
            <p>Secure Access Required</p>
        </div>
        
        ${error ? `<div class="error">${error}</div>` : ''}
        
        <form method="POST" action="/auth/login">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit">Access Documentation</button>
        </form>
        
        <div class="info">
            This documentation contains sensitive information. Please contact your administrator if you need access.
        </div>
    </div>
</body>
</html>`;
}

export const GET: APIRoute = async ({ locals, request }) => {
  const env = locals.runtime?.env || {};
  
  // If auth is disabled, redirect to home
  if (env.ENABLE_AUTH === 'false') {
    const requestUrl = new URL(request.url);
    return Response.redirect(`${requestUrl.origin}/`, 302);
  }
  
  return new Response(generateLoginPage(), {
    headers: { 'Content-Type': 'text/html' }
  });
};

export const POST: APIRoute = async ({ request, locals, cookies }) => {
  const env = locals.runtime?.env || {};
  
  // If auth is disabled, redirect to home
  if (env.ENABLE_AUTH === 'false') {
    const requestUrl = new URL(request.url);
    return Response.redirect(`${requestUrl.origin}/`, 302);
  }
  
  try {
    const formData = await request.formData();
    const username = formData.get('username') as string;
    const password = formData.get('password') as string;

    // Get valid users from environment
    const validUsersString = env.VALID_USERS || '{}';
    let validUsers;
    try {
      validUsers = JSON.parse(validUsersString);
    } catch {
      validUsers = {};
    }
    
    if (!validUsers[username] || validUsers[username] !== password) {
      return new Response(generateLoginPage('Invalid credentials'), {
        status: 401,
        headers: { 'Content-Type': 'text/html' }
      });
    }

    // Generate JWT token
    const payload = {
      username,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    };

    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
    const payloadB64 = btoa(JSON.stringify(payload));
    
    const encoder = new TextEncoder();
    const jwtSecret = env.JWT_SECRET || 'default-secret-change-this';
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(jwtSecret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const signature = await crypto.subtle.sign(
      'HMAC',
      key,
      encoder.encode(`${header}.${payloadB64}`)
    );

    const signatureB64 = btoa(String.fromCharCode(...new Uint8Array(signature)));
    const token = `${header}.${payloadB64}.${signatureB64}`;

    // Set cookie with conditional Secure flag based on protocol
    const requestUrl = new URL(request.url);
    const homeUrl = new URL('/', requestUrl.origin);
    const isHttps = requestUrl.protocol === 'https:';
    const secureFlag = isHttps ? '; Secure' : '';
    
    return new Response('', {
      status: 302,
      headers: {
        'Location': homeUrl.toString(),
        'Set-Cookie': `nexus_auth=${token}; HttpOnly${secureFlag}; SameSite=Lax; Max-Age=${24 * 60 * 60}; Path=/`
      }
    });
  } catch (error) {
    return new Response(generateLoginPage('Authentication error'), {
      status: 500,
      headers: { 'Content-Type': 'text/html' }
    });
  }
};