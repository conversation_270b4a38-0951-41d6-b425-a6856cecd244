import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ request }) => {
  // Clear the authentication cookie and redirect to login
  const requestUrl = new URL(request.url);
  const loginUrl = new URL('/auth/login', requestUrl.origin);
  const isHttps = requestUrl.protocol === 'https:';
  const secureFlag = isHttps ? '; Secure' : '';
  
  return new Response('', {
    status: 302,
    headers: {
      'Location': loginUrl.toString(),
      'Set-Cookie': `nexus_auth=; HttpOnly${secureFlag}; SameSite=Lax; Max-Age=0; Path=/`
    }
  });
};