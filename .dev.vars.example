# Cloudflare Workers Local Development Variables
# This file is used by `wrangler dev` for local development
# Copy this to .dev.vars and update with your values
# DO NOT commit .dev.vars to version control

# Authentication Control
ENABLE_AUTH=false

# JWT Secret for authentication (only needed when ENABLE_AUTH=true)
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# Valid users for basic authentication (JSON format, only needed when ENABLE_AUTH=true)
VALID_USERS={"admin":"admin123","dev":"dev123","docs":"docs123"}

# Environment
ENVIRONMENT=development

# Site URL
SITE_URL=http://localhost:8787