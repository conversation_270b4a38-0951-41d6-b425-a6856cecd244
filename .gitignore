# build output
dist/
build/
.output/
.vercel/

.claude/

# generated types
.astro/

# dependencies
node_modules/

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# environment variables
.env
.env.production
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# macOS-specific files
.DS_Store
.DS_Store?
._*

# Windows specific files
Thumbs.db
ehthumbs.db

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# TypeScript
*.tsbuildinfo

# Cache directories
.cache/
.parcel-cache/
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Cloudflare Workers
.wrangler/
wrangler.toml.bak
.dev.vars

# Vercel
.vercel

# Coverage
coverage/
*.lcov

# Temporary folders
tmp/
temp/

# Optional directories
.npm
.eslintcache
.yarn-integrity

# Playwright (if using for testing)
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Package manager lock files (uncomment the ones you don't use)
# yarn.lock
# package-lock.json
# Keep pnpm-lock.yaml since you're using pnpm
